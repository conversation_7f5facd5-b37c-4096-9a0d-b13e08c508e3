#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
群管插件配置共享功能测试脚本
用于验证修复后的配置共享是否正常工作
"""

import sqlite3
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 导入群管插件
try:
    from awesome_bot.plugins.group_manager import load_config, set_group_share, get_group_shares
    print("✅ 成功导入群管插件模块")
except ImportError as e:
    print(f"❌ 导入群管插件失败: {e}")
    sys.exit(1)

def test_config_share():
    """测试配置共享功能"""
    print("\n🔍 开始测试群管插件配置共享功能...")
    
    # 测试群号
    source_group = 631250950  # a群（源群）
    target_groups = [111111, 222222, 333333, 444444]  # bcdef群（目标群）
    
    print(f"\n📋 测试场景：")
    print(f"   源群：{source_group}")
    print(f"   目标群：{target_groups}")
    
    # 1. 检查源群配置
    print(f"\n1️⃣ 检查源群 {source_group} 的配置...")
    try:
        source_config = load_config(source_group)
        print(f"   ✅ 源群配置加载成功")
        print(f"   - 欢迎消息: {source_config['welcome_msg'][:30]}...")
        print(f"   - 自动审核: {source_config['auto_approve']}")
        print(f"   - 最低等级: {source_config['min_qq_level']}")
        print(f"   - 插件启用: {source_config['plugin_enabled']}")
    except Exception as e:
        print(f"   ❌ 源群配置加载失败: {e}")
        return False
    
    # 2. 设置配置共享（使用新的多配置共享系统）
    print(f"\n2️⃣ 为目标群设置配置共享...")
    for target_group in target_groups:
        try:
            success = set_group_share(target_group, "配置", source_group)
            if success:
                print(f"   ✅ 群 {target_group} 配置共享设置成功")
            else:
                print(f"   ❌ 群 {target_group} 配置共享设置失败")
        except Exception as e:
            print(f"   ❌ 群 {target_group} 配置共享设置异常: {e}")
    
    # 3. 验证共享状态
    print(f"\n3️⃣ 验证共享状态...")
    for target_group in target_groups:
        try:
            shares = get_group_shares(target_group)
            if "配置" in shares and shares["配置"] == source_group:
                print(f"   ✅ 群 {target_group} 共享状态正确: 配置共享自群 {shares['配置']}")
            else:
                print(f"   ❌ 群 {target_group} 共享状态错误: {shares}")
        except Exception as e:
            print(f"   ❌ 群 {target_group} 共享状态检查异常: {e}")
    
    # 4. 测试配置加载（关键测试）
    print(f"\n4️⃣ 测试配置加载（关键测试）...")
    for target_group in target_groups:
        try:
            target_config = load_config(target_group)
            
            # 检查是否正确加载了共享配置
            if (target_config['share_from'] == source_group and 
                target_config['share_type'] == "配置"):
                print(f"   ✅ 群 {target_group} 配置共享生效")
                
                # 验证关键配置项是否与源群一致
                config_matches = (
                    target_config['welcome_msg'] == source_config['welcome_msg'] and
                    target_config['auto_approve'] == source_config['auto_approve'] and
                    target_config['min_qq_level'] == source_config['min_qq_level']
                )
                
                if config_matches:
                    print(f"   ✅ 群 {target_group} 配置内容与源群一致")
                else:
                    print(f"   ⚠️  群 {target_group} 配置内容与源群不一致")
                    print(f"      源群欢迎消息: {source_config['welcome_msg'][:30]}...")
                    print(f"      目标群欢迎消息: {target_config['welcome_msg'][:30]}...")
                    
            else:
                print(f"   ❌ 群 {target_group} 配置共享未生效")
                print(f"      share_from: {target_config['share_from']} (期望: {source_group})")
                print(f"      share_type: {target_config['share_type']} (期望: 配置)")
                
        except Exception as e:
            print(f"   ❌ 群 {target_group} 配置加载异常: {e}")
    
    print(f"\n🎉 测试完成！")
    return True

def check_database_structure():
    """检查数据库结构"""
    print("\n🔍 检查数据库结构...")
    
    try:
        from awesome_bot.plugins.group_manager import data_dir
        db_path = data_dir / "group_manager.db"
        
        if not db_path.exists():
            print(f"   ❌ 数据库文件不存在: {db_path}")
            return False
            
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 检查group_shares表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='group_shares'")
        if cursor.fetchone():
            print("   ✅ group_shares表存在")
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(group_shares)")
            columns = cursor.fetchall()
            print(f"   📋 group_shares表结构: {[col[1] for col in columns]}")
        else:
            print("   ❌ group_shares表不存在")
            
        # 检查group_config表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='group_config'")
        if cursor.fetchone():
            print("   ✅ group_config表存在")
        else:
            print("   ❌ group_config表不存在")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 数据库检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 群管插件配置共享功能测试")
    print("=" * 50)
    
    # 检查数据库结构
    if not check_database_structure():
        print("❌ 数据库结构检查失败，退出测试")
        sys.exit(1)
    
    # 运行测试
    if test_config_share():
        print("\n✅ 测试执行完成")
    else:
        print("\n❌ 测试执行失败")
        sys.exit(1)
