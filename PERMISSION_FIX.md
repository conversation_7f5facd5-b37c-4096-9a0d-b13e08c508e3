# 自定义回复插件权限修复说明

## 问题描述

之前的自定义回复插件存在权限控制问题：
- 非管理员用户发送"自定义"（不带#前缀）也能触发帮助命令
- 这是因为插件使用了`CUSTOM_REPLY_PERMISSION`权限，该权限允许所有非黑名单用户使用

## 修复内容

### 1. 权限修复
将以下命令的权限从`CUSTOM_REPLY_PERMISSION`改为`SUPERUSER`：
- `#自定义` - 帮助命令
- `#变量` - 变量帮助命令  
- `#查词` - 查询关键词
- `#词列表` - 查看关键词列表
- `#定时列表` - 查看定时消息列表
- `#自定义统计` - 查看统计数据

### 2. 代码变更
- 移除了不必要的`CUSTOM_REPLY_PERMISSION`导入
- 所有管理命令现在统一使用NoneBot的全局`SUPERUSER`权限

### 3. 权限说明
现在插件使用两种权限：
- `SUPERUSER`: 用于所有管理命令，只有在`.env`文件中配置的`SUPERUSERS`用户可以使用
- `NOT_BLACKLISTED`: 用于关键词触发，所有非黑名单用户都可以触发关键词回复

## 配置要求

确保在`.env`文件中正确配置超级用户：

```ini
# 全局超级用户 - 可以使用所有管理命令
SUPERUSERS=["你的QQ号"]

# 自定义回复插件的超级用户（建议与上面保持一致）
CUSTOM_REPLY_SUPERUSERS=["你的QQ号"]
```

## 测试验证

修复后的行为：
- ✅ 只有超级用户可以使用`#自定义`等管理命令
- ✅ 非超级用户发送"自定义"不会触发任何响应
- ✅ 关键词回复功能正常工作（非黑名单用户可以触发）
- ✅ 黑名单用户无法触发任何功能

## 注意事项

1. 确保`.env`文件中的QQ号格式正确（字符串数组）
2. 重启机器人后配置才会生效
3. 如果需要多个管理员，在数组中添加多个QQ号：`["123456789", "987654321"]`
